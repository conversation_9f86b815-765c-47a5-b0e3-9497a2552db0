﻿using IdentityModel.AspNetCore.AccessTokenValidation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PayPing.Auth.AccessManagement.Extensions;
using PayPing.Common.Tools;
using Zify.Settlement.Application.Infrastructure.Web;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;

public static class AddIdentityExtension
{
    public static IServiceCollection AddIdentity(this IServiceCollection services, IConfiguration configuration)
    {
        // The Zify identity address is located in the configuration map, not in the settlement section.
        var identityAddress = configuration.Get<SettlementAuthOptions>()?.IdentityAddress;
        var settlementOptions = configuration.GetSection(SettlementAuthOptions.SectionName)
                                             .Get<SettlementAuthOptions>()
                                ?? throw new NullReferenceException();
        services.AddAuthentication(authenticationOptions =>
            {
                authenticationOptions.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                authenticationOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.Authority = "https://oauth.payping.dev";
                options.SaveToken = true;
                options.Audience = settlementOptions.ApiName;
                options.MapInboundClaims = false;
                options.TokenValidationParameters.ValidTypes = ["at+jwt"];
                // if token does not contain a dot, it is a reference token
                options.ForwardDefaultSelector = Selector.ForwardReferenceToken();
                options.TokenValidationParameters.ValidIssuers = settlementOptions.ValidIssuerUrls?.Split(",");
            })
            .AddOAuth2Introspection("Introspection", options =>
            {
                options.SaveToken = true;
                options.Authority = "https://oauth.payping.dev";
                options.ClientId = settlementOptions.ApiName;
                options.ClientSecret = settlementOptions.ApiSecret;
                options.EnableCaching = true;
            });

        services.AddScopeTransformation();

        services.AddAuthorization(options =>
        {
            options.AddPolicy("read",
                policy => policy.RequireClaim("scope", "settlement:read"));

            options.AddPolicy("write",
                policy => policy.RequireClaim("scope", "settlement:write"));

            options.AddPolicy("serviceAdministration",
                policy => policy.RequireClaim("scope", "settlement:admin"));

            options.AddPolicy("accountingAdministration",
                policy => policy
                    .RequireClaim("administration", "SettlementAccounting"));

            options.AddPolicy("walletAdministration",
                policy => policy
                    .RequireClaim("administration", "SettlementWallet"));

            options.AddJustInTimeAccessPolicy();
        });

        services.AddSingleton<IAuthorizationMiddlewareResultHandler, CustomAuthorizationMiddlewareResultHandler>();

        return services;
    }
}
