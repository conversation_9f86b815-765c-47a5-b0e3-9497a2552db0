using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.OrderDetails.Commands;

public sealed class DeleteOrderDetailController : ApiControllerBase
{
    /// <summary>
    /// Delete a specific order detail from an order (only when the order is in Draft status)
    /// </summary>
    [Authorize("write")]
    [HttpDelete("orders/{orderId:guid}/details/{detailId:guid}")]
    [ProducesResponseType<Updated>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> DeleteOrderDetailAsync([FromRoute] Guid orderId, [FromRoute] Guid detailId)
    {
        var command = new DeleteOrderDetailCommand(orderId, detailId);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(_ => Ok(Result.Updated), Problem);
    }
}

public sealed record DeleteOrderDetailCommand(Guid OrderId, Guid DetailId)
    : IRequest<DeleteOrderDetailCommand, Task<ErrorOr<Updated>>>;

public sealed class DeleteOrderDetailCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<DeleteOrderDetailCommand, Task<ErrorOr<Updated>>>
{
    public async Task<ErrorOr<Updated>> Handle(DeleteOrderDetailCommand request, CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser is null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        var order = await dbContext.Orders
            .AsTracking()
            .Include(o => o.OrderDetails)
            .Where(o => o.Id == request.OrderId)
            .Where(o => o.UserId == currentUser.Value)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "درخواست مورد نظر یافت نشد.");

        // Validate order state upfront (domain enforces too)
        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست حذف تسویه امکان پذیر نیست");

        var orderDetail = order.OrderDetails.FirstOrDefault(od => od.Id == request.DetailId);
        if (orderDetail is null)
            return Error.NotFound(description: "آیتم تسویه مورد نظر یافت نشد.");

        if (orderDetail.Status != OrderDetailStatus.Init)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست حذف تسویه امکان پذیر نیست");

        order.RemoveOrderDetail(request.DetailId);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0 ? Result.Updated : Error.Failure(description: "خطا در حذف اطلاعات");
    }
}

public sealed class DeleteOrderDetailCommandValidator : AbstractValidator<DeleteOrderDetailCommand>
{
    public DeleteOrderDetailCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("شناسه درخواست نمی‌تواند خالی باشد.");
        RuleFor(x => x.DetailId)
            .NotEmpty().WithMessage("شناسه آیتم درخواست نمی‌تواند خالی باشد.");
    }
}
