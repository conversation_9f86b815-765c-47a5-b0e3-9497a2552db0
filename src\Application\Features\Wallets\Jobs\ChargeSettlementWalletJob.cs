﻿using DNTPersianUtils.Core;
using Microsoft.EntityFrameworkCore;
using Quartz;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Wallets.Jobs;

[DisallowConcurrentExecution]
public class ChargeSettlementWalletJob(
    IApplicationDbContext dbContext,
    IWalletService walletService,
    IDateTime dateTime) : IJob
{
    private readonly DateTime _targetTime = dateTime.Now.Date.AddHours(-1);
    public async Task Execute(IJobExecutionContext context)
    {
        var wallets = await dbContext.UserWalletInformations
            .AsNoTracking()
            .ToListAsync(context.CancellationToken);

        foreach (var walletInformation in wallets)
        {
            var paymentBalance = await walletService.GetWalletSnapshot(
                walletInformation.PaymentWalletId,
                _targetTime,
                context.CancellationToken);
            if (paymentBalance.IsError)
            {
                return;
            }


        }
    }
}
