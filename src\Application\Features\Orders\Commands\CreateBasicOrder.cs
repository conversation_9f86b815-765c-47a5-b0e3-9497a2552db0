﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Helpers;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders.Commands;

public sealed record CreateBasicOrderResponse(Guid OrderId, Guid OrderDetailId);

public sealed class CreateBasicOrderController : ApiControllerBase
{
    /// <summary>
    /// Handles the creation of a new basic order with one detail record for basic users.
    /// </summary>
    /// <param name="request">
    /// The request containing the details of the order to be created, including the title and amount and optional description.
    /// </param>
    /// <returns>
    /// An <see cref="IActionResult"/> containing the result of the operation:
    /// - A <see cref="CreateBasicOrderCommand"/> with the ID of the created order and detail if successful.
    /// - A <see cref="ProblemDetails"/> object if the request is invalid or an error occurs.
    /// </returns>
    /// <remarks>
    /// This endpoint requires authorization with the "write" policy.
    /// </remarks>
    [HttpPost("create-basic-order")]
    [Authorize("write")]
    [ProducesResponseType<CreateBasicOrderResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateBasicOrder([FromBody] CreateBasicOrderCommand request)
    {
        var result = await Mediator.Send(request, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record CreateBasicOrderCommand(decimal Amount)
    : IRequest<CreateBasicOrderCommand, Task<ErrorOr<CreateBasicOrderResponse>>>;

public sealed class CreateBasicOrderCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IWageCalculatorService wageCalculatorService,
    IDateTime dateTime)
    : IRequestHandler<CreateBasicOrderCommand, Task<ErrorOr<CreateBasicOrderResponse>>>
{
    public async Task<ErrorOr<CreateBasicOrderResponse>> Handle(
        CreateBasicOrderCommand request,
        CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser == null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        if (await IsBannedUser(currentUser.Value, cancellationToken))
        {
            return Error.Validation(
                code: "UserBanned",
                description: "متاسفانه شما دسترسی ثبت درخواست تسویه ندارید. با پشتیبانی در ارتباط باشید");
        }

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null) return Error.NotFound(description: "اطلاعات کاربری یافت نشد");
        if (string.IsNullOrWhiteSpace(userConfig.Iban))
        {
            return Error.Validation(
                code: "IbanRequired",
                description: "برای ثبت درخواست تسویه، لطفا شماره شبا خود را بروزرسانی کنید.");
        }

        var exceedLimitation = await ExceedDailyTransferLimitation(
            userConfig,
            request.Amount,
            cancellationToken);

        if (exceedLimitation.IsError)
            return exceedLimitation.Errors;

        var order = Order.Create(userId: currentUser.Value);
        var orderDetail = OrderDetail.Create(
            userId: order.UserId,
            iban: userConfig.Iban,
            amount: request.Amount,
            wageAmount: wageCalculatorService.Calculate(userConfig, request.Amount));

        order.AddDetail(orderDetail);
        dbContext.Orders.Add(order);

        var result = await dbContext.SaveChangesAsync(cancellationToken);

        return result > 0
            ? new CreateBasicOrderResponse(order.Id, orderDetail.Id)
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }

    private Task<bool> IsBannedUser(int userId, CancellationToken cancellationToken)
    {
        return dbContext.UserConfigs
            .AsNoTracking()
            .Where(u => u.UserId == userId)
            .Select(uc => uc.IsBanned)
            .FirstOrDefaultAsync(cancellationToken);
    }

    private async Task<ErrorOr<Success>> ExceedDailyTransferLimitation(
        UserConfig userConfig,
        decimal amount,
        CancellationToken ct)
    {
        OrderDetailStatus[] statuses = [OrderDetailStatus.InProgress, OrderDetailStatus.Success];

        var totalAmount = await dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.UserId == currentUserService.UserId)
            .Where(x => x.Created >= dateTime.Now.Date && x.Created < dateTime.Now.AddDays(1).Date)
            .Where(x => statuses.Contains(x.Status))
            .SumAsync(x => x.Amount, ct);

        if (totalAmount + amount > userConfig.DailyTransferLimit)
            return Error.Conflict(description:
                string.Format(
                    ErrorMessages.MessageFormats.DailyTransferLimitExceeded,
                    amount.ToCommas(),
                    userConfig.DailyTransferLimit.ToCommas(),
                    totalAmount.ToCommas()
                ));

        return Result.Success;
    }
}

public sealed class CreateBasicOrderCommandValidator : AbstractValidator<CreateBasicOrderCommand>
{
    public CreateBasicOrderCommandValidator(ISettlementSettings settings)
    {
        RuleFor(x => x.Amount)
            .NotEmpty().WithMessage("مبلغ اجباری است")
            .GreaterThan(settings.GetMinSettlementAmount)
            .WithMessage("مبلغ تسویه کمتر از حد مجاز");
    }
}