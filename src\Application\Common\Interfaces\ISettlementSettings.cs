﻿namespace Zify.Settlement.Application.Common.Interfaces;

public interface ISettlementSettings
{

    /// <summary>
    /// Determines whether this user ID is for our wage account or not
    /// </summary>
    bool IsWageUser(int userId);

    /// <summary>
    /// Gets the wage user ID
    /// </summary>
    int WageUserId { get; }

    /// <summary>
    /// Gets whether IBAN inquiry is active
    /// </summary>
    bool IsIbanInquiryActive { get; }

    /// <summary>
    /// Gets whether bank validation is active
    /// </summary>
    bool IsBankValidationActive { get; }

    /// <summary>
    /// Gets the minimum settlement amount
    /// </summary>
    long GetMinSettlementAmount { get; }

    /// <summary>
    /// Gets the maximum settlement amount
    /// </summary>
    long GetMaxSettlementAmount { get; }

    /// <summary>
    /// Gets the maximum amount allowed in the last 24 hours
    /// </summary>
    decimal GetMaxLast24Amount { get; }

    /// <summary>
    /// Gets the default daily transfer limit
    /// </summary>
    decimal DailyTransferDefaultLimit { get; }

    /// <summary>
    /// Gets whether Finnotech service is active
    /// </summary>
    bool IsFinnotechServiceActive { get; }

    /// <summary>
    /// Gets the maximum settlement count per request
    /// </summary>
    int GetMaxSettlementCountPerRequest { get; }

    /// <summary>
    /// Gets the identical request limitation hours
    /// </summary>
    int IdenticalRequestLimitationHours { get; }
}