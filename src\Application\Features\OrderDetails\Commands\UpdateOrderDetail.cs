using DispatchR.Requests.Send;
using DNTPersianUtils.Core;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.InternalServices;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.OrderDetails.Commands;

public sealed record UpdateOrderDetailRequest(
    decimal? Amount,
    string? Iban,
    string? Mobile,
    string? NationalCode,
    string? Description);

public sealed class UpdateOrderDetailController : ApiControllerBase
{
    /// <summary>
    /// Update an existing order's detail (only when the order is in Init status)
    /// </summary>
    [Authorize("write")]
    [HttpPatch("orders/{orderId:guid}/details/{detailId:guid}")]
    [ProducesResponseType<Updated>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UpdateOrderDetailAsync([FromRoute] Guid orderId, [FromRoute] Guid detailId, [FromBody] UpdateOrderDetailRequest request)
    {
        var command = new UpdateOrderDetailCommand(orderId, detailId, request.Amount, request.Iban, request.Mobile, request.NationalCode, request.Description);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(_ => Ok(Result.Updated), Problem);
    }
}

public sealed record UpdateOrderDetailCommand(
    Guid OrderId,
    Guid DetailId,
    decimal? Amount,
    string? Iban,
    string? Mobile,
    string? NationalId,
    string? Description)
    : IRequest<UpdateOrderDetailCommand, Task<ErrorOr<Updated>>>;

public sealed class UpdateOrderDetailCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IWageCalculatorService wageCalculatorService,
    IIbanInquiryService ibanInquiryService,
    IPayeeService payeeService)
    : IRequestHandler<UpdateOrderDetailCommand, Task<ErrorOr<Updated>>>
{
    public async Task<ErrorOr<Updated>> Handle(UpdateOrderDetailCommand request, CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser is null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        var order = await dbContext.Orders
            .AsTracking()
            .Include(o => o.OrderDetails)
            .Where(o => o.Id == request.OrderId)
            .Where(o => o.UserId == currentUser.Value)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "درخواست مورد نظر یافت نشد.");

        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست ویرایش تسویه امکان پذیر نیست");

        var detail = order.OrderDetails.FirstOrDefault(od => od.Id == request.DetailId);
        if (detail is null)
            return Error.NotFound(description: "آیتم تسویه مورد نظر یافت نشد.");

        if (detail.Status != OrderDetailStatus.Init)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست ویرایش تسویه امکان پذیر نیست");

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(uc => uc.UserId == currentUser.Value)
            .FirstOrDefaultAsync(cancellationToken);
        if (userConfig is null) return Error.NotFound(description: "اطلاعات کاربری یافت نشد");

        decimal? newWageAmount = null;
        if (request.Amount.HasValue)
        {
            newWageAmount = wageCalculatorService.Calculate(userConfig, request.Amount.Value);
        }

        Iban? selectedIban = null;
        string? fullName = null;
        Zify.Settlement.Application.Domain.Payee? payeeToSet = null;
        int? payeeIdToSet = null;
        string? mobileToSet = request.Mobile;
        string? nationalIdToSet = request.NationalId;

        if (!string.IsNullOrWhiteSpace(request.Iban))
        {
            var fullNameResult = await ibanInquiryService.TryValidateIbanAndAccountStatus(request.Iban);
            if (fullNameResult.IsError)
                return fullNameResult.Errors;

            selectedIban = Iban.Of(request.Iban);

            var payeeResult = await payeeService.GetOrCreatePayeeAsync(
                new PayeeInfo(nationalIdToSet ?? detail.NationalId, mobileToSet ?? detail.Mobile, selectedIban.Value, fullNameResult.Value),
                userConfig.Id,
                cancellationToken);

            if (payeeResult.IsError)
                return payeeResult.Errors;

            payeeToSet = payeeResult.Value;
            payeeIdToSet = payeeToSet.Id;
            fullName = payeeToSet.FullName;
            mobileToSet ??= payeeToSet.MobileNumbers.FirstOrDefault()?.MobileNumber;
            nationalIdToSet ??= payeeToSet.NationalCode;
        }

        order.UpdateDetail(
            detail,
            selectedIban,
            request.Amount,
            newWageAmount,
            request.Description,
            mobileToSet,
            nationalIdToSet,
            fullName,
            payeeIdToSet,
            payeeToSet);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0 ? Result.Updated : Error.Failure(description: "خطا در حذف اطلاعات");
    }
}

public sealed class UpdateOrderDetailCommandValidator : AbstractValidator<UpdateOrderDetailCommand>
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;
    private readonly ISettlementSettings _settings;

    public UpdateOrderDetailCommandValidator(
        IApplicationDbContext dbContext,
        ISettlementSettings settings,
        ITransferLimitService transferLimitService,
        ICurrentUserService currentUserService)
    {
        _dbContext = dbContext;
        _settings = settings;
        _currentUserService = currentUserService;

        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("شناسه درخواست نمی‌تواند خالی باشد.");
        RuleFor(x => x.DetailId)
            .NotEmpty().WithMessage("شناسه آیتم درخواست نمی‌تواند خالی باشد.");
        RuleFor(x => x.Description)
            .MaximumLength(50).WithMessage("متن توضیحات بیشتر از ۵۰ کارکتر نمی‌تواند باشد");

        RuleFor(x => x)
            .CustomAsync(async (command, context, ct) =>
            {
                var validationData = await GetValidationDataAsync(ct);
                if (validationData is null)
                {
                    context.AddFailure("User not found.");
                    return;
                }

                if (command.Amount.HasValue)
                {
                    var maxSettlementAmount = validationData.MaxSettlementAmount == 0
                        ? settings.GetMaxSettlementAmount
                        : validationData.MaxSettlementAmount;

                    if (command.Amount.Value < settings.GetMinSettlementAmount)
                        context.AddFailure(nameof(command.Amount), "مبلغ تسویه کمتر از حد مجاز");

                    if (command.Amount.Value > maxSettlementAmount)
                        context.AddFailure(nameof(command.Amount), "مبلغ تسویه بیشتر از حد مجاز");
                }

                if (validationData.IsCritical)
                {
                    if (command.Mobile is not null && !command.Mobile.IsValidIranianMobileNumber())
                        context.AddFailure(nameof(command.Mobile), ErrorMessages.InvalidMobileNumber);

                    if (command.NationalId is not null && !command.NationalId.IsValidIranianNationalCode())
                        context.AddFailure(nameof(command.NationalId), ErrorMessages.InvalidNationalCode);

                    if (!string.IsNullOrEmpty(command.Iban) && command.Amount.HasValue)
                    {
                        var limitCheckResult = await transferLimitService.CheckIbanTransferLimitAsync(
                            _currentUserService.UserId,
                            command.Iban,
                            command.Amount.Value,
                            limit: settings.GetMaxLast24Amount, ct);

                        if (limitCheckResult.IsError)
                            context.AddFailure(nameof(command.Iban), limitCheckResult.FirstError.Description);
                    }
                }
            });
    }

    private sealed class UserValidationData
    {
        public bool IsCritical { get; init; }
        public long MaxSettlementAmount { get; init; }
    }

    private async Task<UserValidationData?> GetValidationDataAsync(CancellationToken ct)
    {
        return await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(u => u.UserId == _currentUserService.UserId)
            .Select(u => new UserValidationData
            {
                IsCritical = u.IsCritical,
                MaxSettlementAmount = u.MaxSettlementAmount,
            })
            .FirstOrDefaultAsync(ct);
    }
}