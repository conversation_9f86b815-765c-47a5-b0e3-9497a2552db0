﻿using Microsoft.Extensions.Configuration;

namespace Zify.Settlement.Application.Infrastructure.Configurations;

public class SettlementAuthOptions
{
    public const string SectionName = "Settlement";

    [ConfigurationKeyName("Zify_Identity_Address")]
    public string IdentityAddress { get; set; } = null!;
    public string ApiName { get; set; } = null!;
    public string ApiSecret { get; set; } = null!;
    public string? ValidIssuerUrls { get; set; }
}
