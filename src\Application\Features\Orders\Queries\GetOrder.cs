﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders.Queries;

public sealed class GetOrderController : ApiControllerBase
{
    /// <summary>
    /// Retrieves a specific order by ID with optional details
    /// </summary>
    /// <param name="orderId">The unique identifier of the order</param>
    /// <param name="includeDetails">Whether to include order details in the response</param>
    /// <returns>Order details with conditionally loaded order items</returns>
    [HttpGet("orders/{orderId:guid}")]
    [Authorize("read")]
    [ProducesResponseType<GetOrderResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetOrder(Guid orderId, [FromQuery] bool includeDetails = false)
    {
        var query = new GetOrderQuery(orderId, includeDetails);
        var result = await Mediator.Send(query, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record GetOrderQuery(Guid OrderId, bool IncludeDetails)
    : IRequest<GetOrderQuery, Task<ErrorOr<GetOrderResponse>>>;

public sealed record GetOrderResponse(
    Guid Id,
    string OrderNumber,
    string Title,
    string Description,
    OrderStatus Status,
    decimal TotalAmount,
    decimal TotalWageAmount,
    int UserId,
    DateTimeOffset? ScheduledTime,
    DateTimeOffset? SubmittedTime,
    DateTimeOffset Created,
    DateTimeOffset? LastModified,
    List<GetOrderDetailResponse>? OrderDetails = null)
{
    public static Expression<Func<Order, GetOrderResponse>> BasicSelector() =>
        order => new GetOrderResponse(
            order.Id,
            order.OrderNumber,
            order.Title,
            order.Description,
            order.Status,
            order.TotalAmount,
            order.TotalWageAmount,
            order.UserId,
            order.ScheduledTime,
            order.SubmittedTime,
            order.Created,
            order.LastModified,
            null // No order details
        );

    public static Expression<Func<Order, GetOrderResponse>> DetailedSelector() =>
        order => new GetOrderResponse(
            order.Id,
            order.OrderNumber,
            order.Title,
            order.Description,
            order.Status,
            order.TotalAmount,
            order.TotalWageAmount,
            order.UserId,
            order.ScheduledTime,
            order.SubmittedTime,
            order.Created,
            order.LastModified,
            order.OrderDetails.Select(od => new GetOrderDetailResponse(
                od.Id,
                od.Description,
                od.Status,
                od.Amount,
                od.WageAmount,
                od.Iban,
                od.Mobile,
                od.NationalId,
                od.FullName,
                od.PayeeId,
                od.Created,
                od.LastModified
            )).ToList()
        );
}

public sealed record GetOrderDetailResponse(
    Guid Id,
    string Description,
    OrderDetailStatus Status,
    decimal Amount,
    decimal WageAmount,
    string Iban,
    string? Mobile,
    string? NationalId,
    string? FullName,
    int? PayeeId,
    DateTimeOffset Created,
    DateTimeOffset? LastModified);

public sealed class GetOrderQueryHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<GetOrderQuery, Task<ErrorOr<GetOrderResponse>>>
{
    private static class OrderErrorMessages
    {
        public const string OrderNotFound = "درخواست مورد نظر یافت نشد.";
        public const string Unauthorized = "شما مجوز انجام این عملیات را ندارید.";
    }

    public async Task<ErrorOr<GetOrderResponse>> Handle(
        GetOrderQuery request,
        CancellationToken cancellationToken)
    {
        var currentUserId = currentUserService.UserId;
        if (currentUserId == null)
            return Error.Unauthorized(description: OrderErrorMessages.Unauthorized);

        var query = BuildQuery(request, currentUserId);
        var order = await ExecuteQuery(query, request.IncludeDetails, cancellationToken);

        return order == null
            ? Error.NotFound(description: OrderErrorMessages.OrderNotFound)
            : order;
    }

    private IQueryable<Order> BuildQuery(GetOrderQuery request, int? userId)
    {
        var query = dbContext.Orders
            .AsNoTracking()
            .Where(o => o.Id == request.OrderId && o.UserId == userId);

        if (request.IncludeDetails)
            query = query.Include(o => o.OrderDetails);

        return query;
    }

    private static async Task<GetOrderResponse?> ExecuteQuery(
        IQueryable<Order> query,
        bool includeDetails,
        CancellationToken cancellationToken)
    {
        var selector = includeDetails
            ? GetOrderResponse.DetailedSelector()
            : GetOrderResponse.BasicSelector();

        return await query
            .Select(selector)
            .FirstOrDefaultAsync(cancellationToken);
    }
}

public sealed class GetOrderQueryValidator : AbstractValidator<GetOrderQuery>
{
    public GetOrderQueryValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty()
            .WithMessage("شناسه درخواست نمی‌تواند خالی باشد.");
    }
}
