﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.OrderDetails.Queries;

public sealed class GetOrderDetailsController : ApiControllerBase
{
    /// <summary>
    /// Retrieves all order details for a specific order
    /// </summary>
    /// <param name="orderId">The unique identifier of the order</param>
    /// <returns>List of order details for the specified order</returns>
    [HttpGet("orders/{orderId:guid}/details")]
    [Authorize("read")]
    [ProducesResponseType<GetOrderDetailsResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetOrderDetails(Guid orderId)
    {
        var query = new GetOrderDetailsQuery(orderId);
        var result = await Mediator.Send(query, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record GetOrderDetailsQuery(Guid OrderId)
    : IRequest<GetOrderDetailsQuery, Task<ErrorOr<GetOrderDetailsResponse>>>;

public sealed record GetOrderDetailsResponse(
    List<OrderDetailItemResponse> OrderDetails);

public sealed record OrderDetailItemResponse(
    Guid Id,
    string Description,
    OrderDetailStatus Status,
    decimal Amount,
    decimal WageAmount,
    string Iban,
    string? Mobile,
    string? NationalId,
    string? FullName,
    int? PayeeId,
    DateTimeOffset Created,
    DateTimeOffset? LastModified)
{
    public static Expression<Func<OrderDetail, OrderDetailItemResponse>> OrderDetailSelector() =>
        orderDetail => new OrderDetailItemResponse(
            orderDetail.Id,
            orderDetail.Description,
            orderDetail.Status,
            orderDetail.Amount,
            orderDetail.WageAmount,
            orderDetail.Iban,
            orderDetail.Mobile,
            orderDetail.NationalId,
            orderDetail.FullName,
            orderDetail.PayeeId,
            orderDetail.Created,
            orderDetail.LastModified
        );
}

public sealed class GetOrderDetailsQueryHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<GetOrderDetailsQuery, Task<ErrorOr<GetOrderDetailsResponse>>>
{
    public async Task<ErrorOr<GetOrderDetailsResponse>> Handle(
        GetOrderDetailsQuery request,
        CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser == null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        var orderDetails = await dbContext.OrderDetails
            .AsNoTracking()
            .Where(od => od.OrderId == request.OrderId)
            .Where(od => od.UserId == currentUser.Value)
            .Select(OrderDetailItemResponse.OrderDetailSelector())
            .ToListAsync(cancellationToken);

        if (orderDetails.Count == 0)
            return Error.NotFound(description: "جزئیات درخواست مورد نظر یافت نشد.");

        return new GetOrderDetailsResponse(orderDetails);
    }
}

public sealed class GetOrderDetailsQueryValidator : AbstractValidator<GetOrderDetailsQuery>
{
    public GetOrderDetailsQueryValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty()
            .WithMessage("شناسه درخواست نمی‌تواند خالی باشد.");
    }
}
