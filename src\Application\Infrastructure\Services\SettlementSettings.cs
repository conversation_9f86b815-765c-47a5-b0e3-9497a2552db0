using Microsoft.Extensions.Options;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Services;

public class SettlementSettings(IOptions<UserOptions> options) : ISettlementSettings
{
    private readonly UserOptions _options = options.Value;

    public bool IsWageUser(int userId) => userId == _options.WageUserId;

    public int WageUserId => _options.WageUserId;

    public bool IsIbanInquiryActive => _options.IsIbanInquiryActive;

    public bool IsBankValidationActive => _options.IsBankValidationActive;

    public long GetMinSettlementAmount => _options.MinSettlementAmount;

    public long GetMaxSettlementAmount => _options.MaxSettlementAmount;

    public decimal GetMaxLast24Amount => _options.MaxLast24Amount;

    public decimal DailyTransferDefaultLimit => _options.DailyTransferDefaultLimit;

    public bool IsFinnotechServiceActive => _options.IsFinnotechServiceActive;

    public int GetMaxSettlementCountPerRequest => _options.MaxSettlementCountPerRequest;

    public int IdenticalRequestLimitationHours => _options.IdenticalRequestLimitationHours;
}