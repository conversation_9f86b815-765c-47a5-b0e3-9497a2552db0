syntax = "proto3";

option csharp_namespace = "Settlement";

import "Grpc/GrpcServer/Protos/BaseExceptionResponse.proto";

// SettlementGrpc service provides operations for settlement-related functionality
service SettlementGrpc {
  // Gets the settlement wallet ID for a specific user
  rpc GetSettlementWalletId (GetSettlementWalletIdRequest) returns (GetSettlementWalletIdResponse);
  // Gets the IBAN for a specific user
  rpc GetIban (GetIbanRequest) returns (GetIbanResponse);
}

// ===== GetSettlementWalletId Messages =====

message GetSettlementWalletIdRequest {
  int32 user_id = 1; // Required user identifier.
}

message GetSettlementWalletIdResponse {
  // The response will contain either a wallet ID on success or a problem detail on failure.
  oneof result {
    string settlement_wallet_id = 1; // Settlement wallet ID (GUID as string) on success.
    baseExceptionResponse.ProblemDetail problem_detail = 2; // Error details if the operation failed.
  }
}

// ===== GetIban Messages =====

message GetIbanRequest {
  int32 user_id = 1; // Required user identifier.
}

message GetIbanResponse {
  // The response will contain either an IBAN on success or a problem detail on failure.
  oneof result {
    string iban = 1; // User's IBAN on success.
    baseExceptionResponse.ProblemDetail problem_detail = 2; // Error details if the operation failed.
  }
}