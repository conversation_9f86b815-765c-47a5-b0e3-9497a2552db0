using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Services;

public class CurrentUserService(
    IHttpContextAccessor httpContextAccessor,
    IOptions<UserOptions> options)
    : ICurrentUserService
{
    private readonly UserOptions _options = options.Value;

    public int? UserId
    {
        get
        {
            var userIdClaim = httpContextAccessor.HttpContext?.User.FindFirst("sub")?.Value;

            if (int.TryParse(userIdClaim, out var userId))
                return userId;

            return null;
        }
    }
}