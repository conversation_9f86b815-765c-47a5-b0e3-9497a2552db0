﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public sealed record GetAuthenticatorQrCodeUriResponse(string? QrCodeUri);

public class GetAuthenticatorQrCodeUriController : ApiControllerBase
{
    /// <summary>
    /// Gets the current user's Authenticator Qr Code Uri to. This is the first step for Cash out totp activation.
    /// </summary>
    /// <returns>Authenticator Qr Code Uri wrapped in an object.</returns>
    [HttpGet("users/authenticator-qrcode")]
    [Authorize("write")]
    [ProducesResponseType<GetAuthenticatorQrCodeUriResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetAuthenticatorQrCodeUriAsync()
    {
        var result =
            await Mediator.Send(new GetAuthenticatorQrCodeUriQuery(), HttpContext.RequestAborted);

        return result.Match(Ok, Problem);
    }
}

public sealed class GetAuthenticatorQrCodeUriQuery
    : IRequest<GetAuthenticatorQrCodeUriQuery, Task<ErrorOr<GetAuthenticatorQrCodeUriResponse>>>;

public sealed class GetAuthenticatorQrCodeUriQueryHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IUserProfileService userProfileService,
    ITotpProvider totpProvider
    )
    : IRequestHandler<GetAuthenticatorQrCodeUriQuery, Task<ErrorOr<GetAuthenticatorQrCodeUriResponse>>>
{
    public async Task<ErrorOr<GetAuthenticatorQrCodeUriResponse>> Handle(
        GetAuthenticatorQrCodeUriQuery request,
        CancellationToken cancellationToken)
    {
        if (currentUserService.UserId is null or 0)
        {
            return Error.NotFound(description: "user not found");
        }

        var getUserNameResult =
            await userProfileService.FindUserByIdAsync(currentUserService.UserId.Value, cancellationToken);

        if (getUserNameResult.IsError)
            return getUserNameResult.Errors;

        var userConfig = await dbContext.UserConfigs
            .AsTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig == null)
        {
            return Error.NotFound(description: "configuration not found");
        }

        if (userConfig.AuthenticatorTotpEnabled &&
            !string.IsNullOrWhiteSpace(userConfig.AuthenticatorTotpSecretKey))
        {
            return Error.Forbidden();
        }

        var totpSecretKey = totpProvider.GenerateRandomSecretKey();

        userConfig.AuthenticatorTotpSecretKey = totpSecretKey;
        userConfig.AuthenticatorTotpEnabled = false;

        await dbContext.SaveChangesAsync(cancellationToken);

        var uri = totpProvider.CreateAuthenticatorUri(
            totpSecretKey,
            getUserNameResult.Value.UserName,
            ApplicationConstants.AuthenticatorTotp.DefaultAuthenticatorTotpIssuer);

        return new GetAuthenticatorQrCodeUriResponse(uri);
    }
}
