﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders.Commands;

public sealed record UpdateOrderRequest(
    [Required(AllowEmptyStrings = false, ErrorMessage = "عنوان درخواست اجباری است.")]
    string Title,
    string? Description);

public sealed class UpdateOrderController : ApiControllerBase
{
    /// <summary>
    /// Update an existing order (only when the order is in Draft status)
    /// </summary>
    [Authorize("write")]
    [HttpPatch("orders/{orderId:guid}")]
    [ProducesResponseType<Updated>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UpdateOrderAsync([FromRoute] Guid orderId, [FromBody] UpdateOrderRequest request)
    {
        var command = new UpdateOrderCommand(orderId, request.Title, request.Description);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(_ => Ok(Result.Updated), Problem);
    }
}

public sealed record UpdateOrderCommand(Guid OrderId, string Title, string? Description)
    : IRequest<UpdateOrderCommand, Task<ErrorOr<Updated>>>;

public sealed class UpdateOrderCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<UpdateOrderCommand, Task<ErrorOr<Updated>>>
{
    public async Task<ErrorOr<Updated>> Handle(UpdateOrderCommand request, CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser is null)
            return Error.Unauthorized(description: "شما مجوز انجام این عملیات را ندارید.");

        var order = await dbContext.Orders
            .AsTracking()
            .Where(o => o.Id == request.OrderId)
            .Where(o => o.UserId == currentUser.Value)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "درخواست مورد نظر یافت نشد.");

        // Validate order state upfront (domain enforces too)
        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "به منظور به روزرسانی درخواست تسویه باید در وضعیت پیش نویس باشد");

        order.Update(request.Title, request.Description);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0 ? Result.Updated : Error.Failure(description: "خطا در حذف اطلاعات");
    }
}

public sealed class UpdateOrderCommandValidator : AbstractValidator<UpdateOrderCommand>
{
    public UpdateOrderCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("شناسه درخواست نمی‌تواند خالی باشد.");
    }
}
