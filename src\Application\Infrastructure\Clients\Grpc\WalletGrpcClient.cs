﻿using Ardalis.GuardClauses;
using ErrorOr;
using Google.Protobuf.WellKnownTypes;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Wallet;
using Wallet.Type;
using Zify.Settlement.Application.Common.Helpers;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.GrpcClients;

namespace Zify.Settlement.Application.Infrastructure.Clients.Grpc;

public class WalletGrpcClient(
    WalletGrpc.WalletGrpcClient walletGrpcClient,
    WalletTypeGrpc.WalletTypeGrpcClient walletTypeGrpcClient,
    IUserProfileService userService,
    ILogger<WalletGrpcClient> logger) : IWalletGrpcClient
{
    public async Task<ErrorOr<CreateWalletResponse>> CreateWallet(
        int userId,
        string clientTrackId,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NegativeOrZero(userId, nameof(userId));
        Guard.Against.NullOrEmpty(clientTrackId, nameof(clientTrackId));
        try
        {
            var walletTypes = await walletTypeGrpcClient
                .GetWalletTypesAsync(new GetWalletTypesRequest(), cancellationToken: cancellationToken);

            if (walletTypes.ProblemDetail is not null)
            {
                logger.LogError(
                    "An error occurred while calling GetWalletTypesAsync in CreateWallet. Error details: {ProblemDetails}",
                    JsonSerializer.Serialize(walletTypes.ProblemDetail));

                return Error.Failure(nameof(CreateWallet),
                    "An error occurred while GetWalletTypesAsync in CreateWallet.");
            }

            var settlementWalletType = walletTypes.WalletTypes
                .FirstOrDefault(t => t.Title
                    .Equals("Settlement", StringComparison.InvariantCultureIgnoreCase));

            if (settlementWalletType == null)
            {
                logger.LogError(
                    "An error occurred while calling GetWalletTypesAsync in CreateWallet. Settlement wallet type not found");
                return Error.Failure(nameof(CreateWallet),
                    "An error occurred while GetWalletTypesAsync in CreateWallet. Settlement wallet type not found");
            }

            var userInfo = await userService.GetUserExtraAsync(userId, cancellationToken);
            if (userInfo.IsError)
                return userInfo.Errors;

            var request = new CreateWalletRequest
            {
                ClientId = userId,
                BusinessId = userId.ToString(),

                CoinSymbol = "IRT",
                WalletTypeId = settlementWalletType.Id,

                ClientTrackId = clientTrackId,

                FirstName = userInfo.Value.DisplayName,
                NationalId = userInfo.Value.DisplayNationalCode,
                PhoneNumber = userInfo.Value.PhoneNumber,
                // Optional properties
                LastName = string.Empty
            };
            request.Policies.AddRange(settlementWalletType.WalletTypePolicies.Select(p => p.Id));

            var response = await walletGrpcClient.CreateWalletAsync(request, cancellationToken: cancellationToken);
            if (response.ProblemDetail is null)
            {
                return response;
            }

            logger.LogError(
                "An error occurred while creating the wallet. Error details: {ProblemDetails}",
                JsonSerializer.Serialize(response.ProblemDetail));
            return Error.Failure(nameof(CreateWallet),
                "An error occurred while creating the wallet.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while creating the wallet.");
            return Error.Unexpected(nameof(CreateWallet),
                "An error occurred while creating the wallet.");
        }
    }

    public Task<ErrorOr<GetWalletDetailByIdResponse>> GetWalletDetailsById(
        Guid walletId,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<GetWalletTransactionTotalAmountResponse>> GetWalletTransactionTotalAmount(
        Guid walletId,
        TransactionType transactionType,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async Task<ErrorOr<GetWalletBalanceByIdResponse>> GetWalletBalanceById(
        Guid walletId,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        try
        {
            var request = new GetWalletBalanceByIdRequest
            {
                WalletId = walletId.ToString()
            };

            var response =
                await walletGrpcClient.GetWalletBalanceByIdAsync(request, cancellationToken: cancellationToken);
            if (response.ProblemDetail is null)
            {
                return response;
            }

            logger.LogError(
                "An error occurred while getting wallet balance. Error details: {ProblemDetails}",
                JsonSerializer.Serialize(response.ProblemDetail));

            return Error.Failure(nameof(GetWalletBalanceById),
                "An error occurred while getting wallet balance.");
        }
        catch (Exception e)
        {
            logger.LogError(e, "An error occurred while getting wallet balance.");
            return Error.Unexpected(nameof(GetWalletBalanceById),
                "An error occurred while getting wallet balance.");
        }
    }

    public async Task<ErrorOr<GetWalletsBalanceAtTimeResponse>> GetWalletSnapshot(
        Guid walletId,
        DateTime targetTime,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new GetWalletsBalanceAtTimeRequest
            {
                WalletIds = { walletId.ToString() },
                TargetTime = Timestamp.FromDateTime(targetTime.ToUniversalTime())
            };

            var response = await walletGrpcClient.GetWalletsBalanceAtTimeAsync(
                request,
                cancellationToken: cancellationToken);
            if (response.ProblemDetail is null)
            {
                return response;
            }

            logger.LogError(
                "An error occurred while fetching wallet snapshot for WalletId: {WalletId}. Error details: {ProblemDetails}",
                walletId,
                JsonSerializer.Serialize(response.ProblemDetail));

            return Error.Failure(nameof(GetWalletSnapshot),
                "An error occurred while fetching wallet snapshot.");
        }
        catch (Exception e)
        {
            logger.LogError(e,
                "An unexpected error occurred while fetching wallet snapshot for WalletId: {WalletId}.", walletId);
            return Error.Unexpected(nameof(GetWalletSnapshot),
                "An unexpected error occurred while fetching wallet snapshot.");
        }
    }

    public async Task<ErrorOr<BlockWalletResponse>> Freeze(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        Guard.Against.NullOrEmpty(correlationId, nameof(correlationId));
        Guard.Against.NegativeOrZero(amount, nameof(amount));
        try
        {
            var response = await walletGrpcClient.BlockWalletAsync(new BlockWalletRequest
            {
                WalletId = walletId.ToString(),
                ClientBlockId = correlationId.ToString(),
                Amount = amount.ToWalletDecimalValue()
            }, cancellationToken: cancellationToken);

            if (response.ProblemDetail is null)
            {
                return response;
            }

            // TODO: Check not enough balance
            //if (response.ProblemDetail.Errors.Any(x => x.Code == "1"))
            //{
            //    return Error.Validation("NotEnoughBalance", "not enough balance")
            //}

            logger.LogError(
                "An error occurred while freezing the wallet. Error details: {ProblemDetails}",
                JsonSerializer.Serialize(response.ProblemDetail));

            return Error.Failure(nameof(Freeze),
                "An error occurred while freezing the wallet.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while freezing the wallet.");
            return Error.Unexpected(nameof(Freeze),
                "An error occurred while freezing the wallet.");
        }
    }

    public Task<ErrorOr<UnBlockWalletResponse>> Unfreeze(
        Guid walletId,
        Guid correlationId,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<UnBlockWalletResponse>> UnfreezeAndWithdraw(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<DepositWalletResponse>> Deposit(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        string description,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<WithdrawWalletResponse>> Withdraw(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        string description,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async Task<ErrorOr<TransferResponse>> Transfer(
        Guid sourceWalletId,
        Guid destinationWalletId,
        Guid correlationId,
        decimal amount,
        decimal fee = 0,
        string? description = null,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(sourceWalletId, nameof(sourceWalletId));
        Guard.Against.NullOrEmpty(destinationWalletId, nameof(destinationWalletId));
        Guard.Against.NullOrEmpty(correlationId, nameof(correlationId));
        Guard.Against.NegativeOrZero(amount, nameof(amount));
        try
        {
            var response = await walletGrpcClient.TransferAsync(new TransferRequest
            {
                SourceWalletId = sourceWalletId.ToString(),
                DestinationWalletId = destinationWalletId.ToString(),
                CorrelationId = correlationId.ToString(),
                Amount = amount.ToWalletDecimalValue(),
                Wage = fee.ToWalletDecimalValue(),
                SourceWalletDescription = description,
                DestinationWalletDescription = description,

                MasterWalletId = null,
                MasterWalletDescription = null
            }, cancellationToken: cancellationToken);

            if (response.ProblemDetail is null)
            {
                return response;
            }

            logger.LogError(
                "An error occurred while processing the wallet transfer request. Error details: {ProblemDetails}",
                JsonSerializer.Serialize(response.ProblemDetail));
            return Error.Failure(nameof(Transfer),
                "An error occurred while processing the wallet transfer request.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while processing the wallet transfer request.");
            return Error.Unexpected(nameof(Transfer),
                "An error occurred while processing the wallet transfer request.");
        }
    }
}