using Ardalis.GuardClauses;
using System.Security.Cryptography;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;
public class Order : AuditableEntity
{
    public Guid Id { get; private init; }
    public string OrderNumber { get; private set; } = string.Empty;
    public WalletId? WalletBlockCorrelationId { get; private set; }
    public WalletId? WalletWithdrawCorrelationId { get; private set; }
    public string Title { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public OrderStatus Status { get; private set; }
    public decimal TotalAmount { get; private set; }
    public decimal TotalWageAmount { get; private set; }
    public int UserId { get; private set; }
    public DateTimeOffset? ScheduledTime { get; private set; }
    public DateTimeOffset? SubmittedTime { get; private set; }

    private readonly List<OrderDetail> _orderDetails = [];
    public IReadOnlyList<OrderDetail> OrderDetails => _orderDetails.AsReadOnly();

    private Order() { }
    public static Order Create(int userId,
        string? title = null,
        string? description = null,
        DateTimeOffset? scheduledTime = null)
    {
        Guard.Against.NegativeOrZero(userId, nameof(userId));

        var orderId = Guid.CreateVersion7();
        var orderNumber = GenerateOrderNumber(orderId);
        return new Order
        {
            Id = orderId,
            OrderNumber = orderNumber,
            Title = title ?? $"دستور پرداخت - {orderNumber}",
            Description = description ?? $"دستور پرداخت - {orderNumber}",
            Status = OrderStatus.Draft,
            ScheduledTime = scheduledTime,
            UserId = userId
        };
    }

    public void AddDetail(OrderDetail orderDetail)
    {
        Guard.Against.Null(orderDetail, nameof(orderDetail));

        if (Status != OrderStatus.Draft)
            throw new InvalidOperationException("Cannot add order details to a non-draft order");

        if (orderDetail.Status != OrderDetailStatus.Init)
            throw new InvalidOperationException("Order detail is not in init status");

        orderDetail.SetOrder(Id);
        _orderDetails.Add(orderDetail);

        RecalculateTotals();
    }

    public void AddDetails(List<OrderDetail> details)
    {
        Guard.Against.NullOrEmpty(details, nameof(details));

        foreach (var detail in details)
        {
            AddDetail(detail);
        }
    }

    public void RemoveOrderDetail(Guid orderDetailId)
    {
        if (Status != OrderStatus.Draft)
            throw new InvalidOperationException("Cannot remove order details from a non-draft order");

        var orderDetail = _orderDetails.FirstOrDefault(od => od.Id == orderDetailId);
        if (orderDetail == null) return;

        _orderDetails.Remove(orderDetail);
        RecalculateTotals();
    }

	    public void UpdateDetail(
	        OrderDetail orderDetail,
	        Iban? iban,
	        decimal? amount,
	        decimal? wageAmount,
	        string? description,
	        string? mobile,
	        string? nationalId,
	        string? fullName,
	        int? payeeId = null,
	        Payee? payee = null)
	    {
	        if (Status != OrderStatus.Draft)
	            throw new InvalidOperationException("Cannot update order details from a non-draft order");

	        if (!_orderDetails.Contains(orderDetail))
	            throw new InvalidOperationException("Specified order detail does not belong to this order");

	        orderDetail.Update(iban, amount, wageAmount, description, mobile, nationalId, fullName, payeeId, payee);
	        RecalculateTotals();
	    }


    public void Update(string? title, string? description)
    {
        if (Status != OrderStatus.Draft)
            throw new InvalidOperationException("Cannot remove order details from a non-draft order");

        Title = title ?? $"دستور پرداخت - {OrderNumber}";
        Description = description ?? $"دستور پرداخت - {OrderNumber}";
    }

    public void WalletProcessing()
    {
        if (Status != OrderStatus.Draft)
            throw new InvalidOperationException($"Order with id {Id} is not in draft status.");

        if (_orderDetails.Count == 0)
            throw new InvalidOperationException("Cannot submit order without order details");

        WalletBlockCorrelationId = WalletId.New();
        Status = OrderStatus.WalletProcessing;
    }

    public void Submit(DateTimeOffset submitTime)
    {
        if (Status != OrderStatus.WalletProcessing)
            throw new InvalidOperationException($"Order with id {Id} is not in initialized status.");

        if (_orderDetails.Count == 0)
            throw new InvalidOperationException("Cannot submit order without order details");

        Status = OrderStatus.Submitted;
        SubmittedTime = submitTime;
    }

    public void Cancel()
    {
        if (Status != OrderStatus.Draft)
        {
            throw new InvalidOperationException($"Order with id {Id} can not be cancelled.");
        }

        Status = OrderStatus.Cancelled;
    }

    public void Process()
    {
        if (Status != OrderStatus.Submitted)
        {
            throw new InvalidOperationException($"Order with id {Id} is not in submitted status.");
        }

        Status = OrderStatus.Processing;
    }

    public void Complete()
    {
        if (Status != OrderStatus.Processing)
        {
            throw new InvalidOperationException($"Order with id {Id} is not in processing status.");
        }

        foreach (var orderDetail in _orderDetails)
        {
            orderDetail.SetStatus(OrderDetailStatus.InProgress);
        }
        Status = OrderStatus.Completed;
    }

    public void Fail()
    {
        if (Status is OrderStatus.Completed or OrderStatus.Cancelled)
        {
            throw new InvalidOperationException($"Order with id {Id} cannot be failed from status {Status}.");
        }

        foreach (var orderDetail in _orderDetails)
        {
            orderDetail.SetStatus(OrderDetailStatus.Failed);
        }
        Status = OrderStatus.Failed;
    }

    private void RecalculateTotals()
    {
        TotalAmount = _orderDetails.Sum(od => od.Amount);
        TotalWageAmount = _orderDetails.Sum(od => od.WageAmount);
    }

    private static string GenerateOrderNumber(Guid id)
    {
        var datePrefix = DateTime.UtcNow.ToString("yyMMddHHmmss");

        Span<char> guidChars = stackalloc char[32];
        id.TryFormat(guidChars, out _, "N");
        ReadOnlySpan<char> guidSuffix = guidChars.Slice(24, 8);

        Span<byte> randomBytes = stackalloc byte[4];
        RandomNumberGenerator.Fill(randomBytes);

        Span<char> hexChars = stackalloc char[8];
        Convert.TryToHexString(randomBytes, hexChars, out _);

        return $"ORD-{datePrefix}-{guidSuffix}-{hexChars}";
    }

    public Order Clone()
    {
        var clonedOrder = Create(UserId, Title, Description, ScheduledTime);

        foreach (var clonedDetail in
                 _orderDetails.Select(orderDetail => OrderDetail.Create(
                     userId: orderDetail.UserId,
                     iban: orderDetail.Iban,
                     amount: orderDetail.Amount,
                     wageAmount: orderDetail.WageAmount,
                     payeeId: orderDetail.PayeeId,
                     nationalId: orderDetail.NationalId,
                     mobile: orderDetail.Mobile,
                     fullname: orderDetail.FullName,
                     description: orderDetail.Description)))
        {
            clonedOrder.AddDetail(clonedDetail);
        }

        return clonedOrder;
    }
}

public enum OrderStatus
{
    /// <summary>
    /// The order is created but not yet submitted for processing.
    /// </summary>
    Draft,

    /// <summary>
    /// The order is being processed in the wallet system, such as blocking funds.
    /// </summary>
    WalletProcessing,

    /// <summary>
    /// The order has been submitted and is awaiting processing by the bank system.
    /// </summary>
    Submitted,

    /// <summary>
    /// The order is currently being processed by the bank system.
    /// </summary>
    Processing,

    /// <summary>
    /// The order has been successfully completed and processed.
    /// </summary>
    Completed,

    /// <summary>
    /// The order has been cancelled and will not be processed further.
    /// </summary>
    Cancelled,

    /// <summary>
    /// The order has been failed.
    /// </summary>
    Failed,
}


