﻿using DispatchR.Extensions;
using FluentValidation;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PayPing.Integrations.AdminSDK;
using PayPing.User.AdminSDK.Configuration;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.InternalServices;
using Zify.Settlement.Application.Features.CashOut.OldSettlement.Infrastructure;
using Zify.Settlement.Application.Infrastructure.Configurations;
using Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
using Zify.Settlement.Application.Infrastructure.Persistence;
using Zify.Settlement.Application.Infrastructure.Persistence.Interceptors;
using Zify.Settlement.Application.Infrastructure.Services;
using Zify.Settlement.Application.Infrastructure.Services.Bank;
using Zify.Settlement.Application.Infrastructure.Services.EzPay;
using Zify.Settlement.Application.Infrastructure.Services.IbanInquiry;
using Zify.Settlement.Application.Infrastructure.Services.Users;
using Zify.Settlement.Application.Infrastructure.Services.Wallet;
using Zify.Settlement.Application.Infrastructure.Utilities;
using Zify.Settlement.Application.InternalServices;

namespace Zify.Settlement.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddConfigurations(configuration);
        services.AddGrpcClients(configuration);

        services.AddDispatchR(typeof(DependencyInjection).Assembly);
        services.AddValidatorsFromAssembly(typeof(DependencyInjection).Assembly, includeInternalTypes: true);
        return services;
    }

    public static IServiceCollection AddInfrastructure(this IServiceCollection services,
        IConfiguration configuration,
        IHostEnvironment environment)
    {
        services.AddCustomSwagger();
        services.AddIdentity(configuration);
        // Add API Versioning
        services.AddCustomVersioning();
        // Add gRPC Server
        services.AddGrpcServer(environment);

        //Register Admin SDKs
        var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>() ?? throw new NullReferenceException();
        services.AddUserAdminSdk(
            new Uri(discoveryOptions.UserServiceAddress));
        services.AddIntegrations(
            new Uri(discoveryOptions.IntegrationServiceAddress));

        // Register services
        services.AddScoped<IUserProfileService, UserProfileService>();
        services.AddScoped<IWageCalculatorService, WageCalculatorService>();
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddScoped<ISettlementSettings, SettlementSettings>();
        services.AddTransient<IDateTime, DateTimeService>();
        services.AddScoped<IEzPayService, EzPayService>();
        services.AddScoped<IIbanInquiryService, EzPayIbanInquiryService>();
        services.AddScoped<IWalletService, WalletService>();
        services.AddScoped<IEventServiceWrapper, EventServiceWrapper>();
        services.AddScoped<IPayeeService, PayeeService>();
        services.AddScoped<ITransferLimitService, TransferLimitService>();
        services.AddScoped<IBankService, BankService>();

        // Register Http Clients
        services.AddEzPayRefitClient(configuration);
        services.AddInquiryHttpClient(configuration);

        services.AddQuartzBackgroundJobs(configuration);

        // Register Utilities
        services.AddScoped<ITotpProvider, TotpProvider>();

        // Register Redis
        services.AddRedis(configuration);

        // Register DbContext
        services.AddScoped<UpdateAuditableInterceptor>();
        services.AddDbContext<IApplicationDbContext, ApplicationDbContext>((sp, options) => options
            .UseNpgsql(configuration
                .GetConnectionString("SettlementDb"), b => b
                .MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName))
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
            .AddInterceptors(
                sp.GetRequiredService<UpdateAuditableInterceptor>()
            ));

        services.AddOldSettlementDbContext(configuration);
        
        // Register MassTransit
        var rabbitOptions = configuration.Get<RabbitMqOptions>()
                            ?? throw new ArgumentNullException(nameof(RabbitMqOptions));
        services.AddMassTransit(x =>
        {
            x.UsingRabbitMq((context, cfg) =>
            {
                cfg.Host(new Uri(rabbitOptions.RabbitUri), "/", c =>
                {
                    c.Username(rabbitOptions.RabbitUsername);
                    c.Password(rabbitOptions.RabbitPassword);
                });

                cfg.ConfigureEndpoints(context);
            });
        });

        return services;
    }
}