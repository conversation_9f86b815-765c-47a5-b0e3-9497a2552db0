using DispatchR.Requests;
using Grpc.Core;
using Microsoft.Extensions.Logging;
using Settlement;
using System.Text.Json;
using Zify.Settlement.Application.Features.UserConfigurations;
using Zify.Settlement.Application.Features.Wallets.Queries;
using GetIbanResponse = Settlement.GetIbanResponse;
using GetSettlementWalletIdResponse = Settlement.GetSettlementWalletIdResponse;

namespace Zify.Settlement.Application.Infrastructure.Services.Grpc;

/// <summary>
/// gRPC service implementation for settlement-related operations.
/// Provides remote access to settlement functionality for other microservices.
/// Note: General exception handling is managed by the ExceptionHandlerInterceptor.
/// </summary>
public sealed class SettlementGrpcService(
    IMediator mediator,
    ILogger<SettlementGrpcService> logger) : SettlementGrpc.SettlementGrpcBase
{
    /// <summary>
    /// Gets the settlement wallet ID for a specific user.
    /// </summary>
    /// <param name="request">The request containing the user ID</param>
    /// <param name="context">The server call context</param>
    /// <returns>The settlement wallet ID response</returns>
    public override async Task<GetSettlementWalletIdResponse> GetSettlementWalletId(
        GetSettlementWalletIdRequest request,
        ServerCallContext context)
    {
        // Using a logger scope adds context to all logs within this method.
        using var _ = logger.BeginScope("Processing GetSettlementWalletId for UserId: {UserId}", request.UserId);

        logger.LogInformation("Initiating GetSettlementWalletId request.");

        // Validate request
        if (request.UserId <= 0)
        {
            logger.LogWarning("Invalid UserId provided: {UserId}", request.UserId);
            var problemDetail = new BaseExceptionResponse.ProblemDetail();
            problemDetail.Errors.Add(new BaseExceptionResponse.ErrorDetail { Code = "InvalidUserId", Message = "User ID must be greater than 0." });

            return new GetSettlementWalletIdResponse { ProblemDetail = problemDetail };
        }

        // Execute the query using the existing handler
        var query = new GetSettlementWalletIdQuery(request.UserId);
        var result = await mediator.Send(query, context.CancellationToken);

        if (result.IsError)
        {
            logger.LogWarning("GetSettlementWalletId failed. Errors: {Errors}", JsonSerializer.Serialize(result.Errors));

            var problemDetail = new BaseExceptionResponse.ProblemDetail();
            problemDetail.Errors.AddRange(result.Errors.Select(MapErrorToGrpcError));

            return new GetSettlementWalletIdResponse { ProblemDetail = problemDetail };
        }

        logger.LogInformation("Successfully retrieved settlement wallet ID.");

        return new GetSettlementWalletIdResponse
        {
            SettlementWalletId = result.Value.SettlementWalletId.ToString()
        };
    }

    /// <summary>
    /// Gets the IBAN for a specific user.
    /// </summary>
    /// <param name="request">The request containing the user ID</param>
    /// <param name="context">The server call context</param>
    /// <returns>The IBAN response</returns>
    public override async Task<GetIbanResponse> GetIban(
        GetIbanRequest request,
        ServerCallContext context)
    {
        // Using a logger scope adds context to all logs within this method.
        using var _ = logger.BeginScope("Processing GetIban for UserId: {UserId}", request.UserId);

        logger.LogInformation("Initiating GetIban request.");

        // Validate request
        if (request.UserId <= 0)
        {
            logger.LogWarning("Invalid UserId provided: {UserId}", request.UserId);
            var problemDetail = new BaseExceptionResponse.ProblemDetail();
            problemDetail.Errors.Add(new BaseExceptionResponse.ErrorDetail { Code = "InvalidUserId", Message = "User ID must be greater than 0." });

            return new GetIbanResponse { ProblemDetail = problemDetail };
        }

        // Execute the query using the existing handler
        var query = new GetIbanByUserIdQuery(request.UserId);
        var result = await mediator.Send(query, context.CancellationToken);

        if (result.IsError)
        {
            logger.LogWarning("GetIban failed. Errors: {Errors}", JsonSerializer.Serialize(result.Errors));

            var problemDetail = new BaseExceptionResponse.ProblemDetail();
            problemDetail.Errors.AddRange(result.Errors.Select(MapErrorToGrpcError));

            return new GetIbanResponse { ProblemDetail = problemDetail };
        }

        logger.LogInformation("Successfully retrieved IBAN.");

        return new GetIbanResponse
        {
            Iban = result.Value.Iban ?? string.Empty
        };
    }

    /// <summary>
    /// Maps ErrorOr.Error to a gRPC ErrorDetail.
    /// </summary>
    private static BaseExceptionResponse.ErrorDetail MapErrorToGrpcError(ErrorOr.Error error) =>
        new()
        {
            Code = error.Code,
            Message = error.Description
        };
}
