using DispatchR.Requests;
using ErrorOr;
using Grpc.Core;
using Microsoft.Extensions.Logging;
using Moq;
using Settlement;
using Shouldly;
using Zify.Settlement.Application.Features.UserConfigurations;
using Zify.Settlement.Application.Features.Wallets.Queries;
using Zify.Settlement.Application.Infrastructure.Services.Grpc;
using GetSettlementWalletIdResponse = Settlement.GetSettlementWalletIdResponse;

namespace Zify.Settlement.Application.UnitTests.Infrastructure.Services.Grpc;

public class SettlementGrpcServiceTests
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly Mock<ILogger<SettlementGrpcService>> _mockLogger;
    private readonly Mock<ServerCallContext> _mockContext;
    private readonly SettlementGrpcService _service;

    public SettlementGrpcServiceTests()
    {
        _mockMediator = new Mock<IMediator>();
        _mockLogger = new Mock<ILogger<SettlementGrpcService>>();
        _mockContext = new Mock<ServerCallContext>();

        _service = new SettlementGrpcService(_mockMediator.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetSettlementWalletId_WithValidUserId_ShouldReturnWalletId()
    {
        // Arrange
        const int userId = 123;
        var expectedWalletId = Guid.NewGuid();
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        var queryResponse = new Zify.Settlement.Application.Features.Wallets.Queries.GetSettlementWalletIdResponse(expectedWalletId);
        _mockMediator.Setup(x => x.Send(
                It.Is<GetSettlementWalletIdQuery>(q => q.UserId == userId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.SettlementWalletId.ShouldBe(expectedWalletId.ToString());
        result.ProblemDetail.ShouldBeNull();

        // Verify mediator was called correctly
        _mockMediator.Verify(x => x.Send(
            It.Is<GetSettlementWalletIdQuery>(q => q.UserId == userId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(-100)]
    public async Task GetSettlementWalletId_WithInvalidUserId_ShouldReturnProblemDetail(int invalidUserId)
    {
        // Arrange
        var request = new GetSettlementWalletIdRequest { UserId = invalidUserId };

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.SettlementWalletId.ShouldBeNullOrEmpty();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.Count.ShouldBe(1);
        result.ProblemDetail.Errors[0].Code.ShouldBe("InvalidUserId");
        result.ProblemDetail.Errors[0].Message.ShouldBe("User ID must be greater than 0.");

        // Verify mediator was not called
        _mockMediator.Verify(x => x.Send(
            It.IsAny<GetSettlementWalletIdQuery>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetSettlementWalletId_WhenMediatorReturnsError_ShouldReturnProblemDetail()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        var error = Error.Validation("Settlement wallet id not found");
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(error);

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.SettlementWalletId.ShouldBeNullOrEmpty();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.Count.ShouldBe(1);
        result.ProblemDetail.Errors[0].Code.ShouldBe(error.Code);
        result.ProblemDetail.Errors[0].Message.ShouldBe(error.Description);
    }

    [Fact]
    public async Task GetSettlementWalletId_WhenMediatorReturnsMultipleErrors_ShouldReturnAllErrors()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        var errors = new List<Error>
        {
            Error.Validation("Error1", "First error"),
            Error.Validation("Error2", "Second error")
        };

        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(errors);

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.SettlementWalletId.ShouldBeNullOrEmpty();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.Count.ShouldBe(2);
        result.ProblemDetail.Errors[0].Code.ShouldBe("Error1");
        result.ProblemDetail.Errors[0].Message.ShouldBe("First error");
        result.ProblemDetail.Errors[1].Code.ShouldBe("Error2");
        result.ProblemDetail.Errors[1].Message.ShouldBe("Second error");
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldLogInformationMessages_WhenSuccessful()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };
        var expectedWalletId = Guid.NewGuid();

        var queryResponse = new Zify.Settlement.Application.Features.Wallets.Queries.GetSettlementWalletIdResponse(expectedWalletId);
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        VerifyLogWasCalled(LogLevel.Information, "Initiating GetSettlementWalletId request.");
        VerifyLogWasCalled(LogLevel.Information, "Successfully retrieved settlement wallet ID.");
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldLogWarning_WhenInvalidUserIdProvided()
    {
        // Arrange
        const int invalidUserId = -1;
        var request = new GetSettlementWalletIdRequest { UserId = invalidUserId };

        // Act
        await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        VerifyLogWasCalled(LogLevel.Warning, $"Invalid UserId provided: {invalidUserId}");
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldLogWarning_WhenMediatorReturnsError()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        var error = Error.Validation("TestError", "Test error description");
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(error);

        // Act
        await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        VerifyLogWasCalled(LogLevel.Warning, "GetSettlementWalletId failed. Errors:");
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldLogCorrectly_WhenProcessingRequest()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };
        var expectedWalletId = Guid.NewGuid();

        var queryResponse = new Zify.Settlement.Application.Features.Wallets.Queries.GetSettlementWalletIdResponse(expectedWalletId);
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        VerifyLogWasCalled(LogLevel.Information, "Initiating GetSettlementWalletId request.");
        VerifyLogWasCalled(LogLevel.Information, "Successfully retrieved settlement wallet ID.");
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldCallMediator_WhenValidRequest()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        var queryResponse = new Zify.Settlement.Application.Features.Wallets.Queries.GetSettlementWalletIdResponse(Guid.NewGuid());
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        _mockMediator.Verify(x => x.Send(
            It.Is<GetSettlementWalletIdQuery>(q => q.UserId == userId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [InlineData(1)]
    [InlineData(999)]
    [InlineData(int.MaxValue)]
    public async Task GetSettlementWalletId_WithValidPositiveUserIds_ShouldCallMediator(int validUserId)
    {
        // Arrange
        var request = new GetSettlementWalletIdRequest { UserId = validUserId };
        var expectedWalletId = Guid.NewGuid();

        var queryResponse = new Zify.Settlement.Application.Features.Wallets.Queries.GetSettlementWalletIdResponse(expectedWalletId);
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.SettlementWalletId.ShouldBe(expectedWalletId.ToString());
        result.ProblemDetail.ShouldBeNull();

        _mockMediator.Verify(x => x.Send(
            It.Is<GetSettlementWalletIdQuery>(q => q.UserId == validUserId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldSerializeErrorsCorrectly_WhenLoggingErrors()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        var error = Error.Validation("TestError", "Test error description");
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(error);

        // Act
        await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("GetSettlementWalletId failed. Errors:") &&
                                             v.ToString()!.Contains("TestError")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldMapErrorCorrectly_WhenSingleErrorReturned()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };

        var error = Error.NotFound("WalletNotFound", "Settlement wallet not found for user");
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(error);

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.Count.ShouldBe(1);
        result.ProblemDetail.Errors[0].Code.ShouldBe("WalletNotFound");
        result.ProblemDetail.Errors[0].Message.ShouldBe("Settlement wallet not found for user");
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldHandleEmptyGuid_WhenMediatorReturnsEmptyWalletId()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };
        var emptyWalletId = Guid.Empty;

        var queryResponse = new Zify.Settlement.Application.Features.Wallets.Queries.GetSettlementWalletIdResponse(emptyWalletId);
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.SettlementWalletId.ShouldBe(Guid.Empty.ToString());
        result.ProblemDetail.ShouldBeNull();
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldNotCallMediator_WhenValidationFails()
    {
        // Arrange
        const int invalidUserId = 0;
        var request = new GetSettlementWalletIdRequest { UserId = invalidUserId };

        // Act
        await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        _mockMediator.Verify(x => x.Send(
            It.IsAny<GetSettlementWalletIdQuery>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldReturnCorrectResponseStructure_WhenSuccessful()
    {
        // Arrange
        const int userId = 123;
        var request = new GetSettlementWalletIdRequest { UserId = userId };
        var expectedWalletId = Guid.NewGuid();

        var queryResponse = new Zify.Settlement.Application.Features.Wallets.Queries.GetSettlementWalletIdResponse(expectedWalletId);
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetSettlementWalletIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<GetSettlementWalletIdResponse>();
        result.SettlementWalletId.ShouldNotBeNullOrEmpty();
        result.ProblemDetail.ShouldBeNull();
    }

    [Fact]
    public async Task GetSettlementWalletId_ShouldReturnCorrectResponseStructure_WhenError()
    {
        // Arrange
        const int invalidUserId = -1;
        var request = new GetSettlementWalletIdRequest { UserId = invalidUserId };

        // Act
        var result = await _service.GetSettlementWalletId(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<GetSettlementWalletIdResponse>();
        result.SettlementWalletId.ShouldBeNullOrEmpty();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.ShouldNotBeEmpty();
    }

    #region GetIban Tests

    [Fact]
    public async Task GetIban_WithValidUserId_ShouldReturnIban()
    {
        // Arrange
        const int userId = 123;
        const string expectedIban = "**************************";
        var request = new GetIbanRequest { UserId = userId };

        var queryResponse = new GetUserIbanResponse(expectedIban);
        _mockMediator.Setup(x => x.Send(
                It.Is<GetIbanByUserIdQuery>(q => q.UserId == userId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        var result = await _service.GetIban(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.Iban.ShouldBe(expectedIban);
        result.ProblemDetail.ShouldBeNull();
    }

    [Fact]
    public async Task GetIban_WithNullIban_ShouldReturnEmptyString()
    {
        // Arrange
        const int userId = 123;
        var request = new GetIbanRequest { UserId = userId };

        var queryResponse = new GetUserIbanResponse(null);
        _mockMediator.Setup(x => x.Send(
                It.Is<GetIbanByUserIdQuery>(q => q.UserId == userId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        var result = await _service.GetIban(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.Iban.ShouldBe(string.Empty);
        result.ProblemDetail.ShouldBeNull();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(-100)]
    public async Task GetIban_WithInvalidUserId_ShouldReturnProblemDetail(int invalidUserId)
    {
        // Arrange
        var request = new GetIbanRequest { UserId = invalidUserId };

        // Act
        var result = await _service.GetIban(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.Iban.ShouldBeNullOrEmpty();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.ShouldHaveSingleItem();
        result.ProblemDetail.Errors[0].Code.ShouldBe("InvalidUserId");
        result.ProblemDetail.Errors[0].Message.ShouldBe("User ID must be greater than 0.");
    }

    [Fact]
    public async Task GetIban_WhenMediatorReturnsError_ShouldReturnProblemDetail()
    {
        // Arrange
        const int userId = 123;
        var request = new GetIbanRequest { UserId = userId };

        var error = Error.NotFound("UserNotFound", "User not found");
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetIbanByUserIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(error);

        // Act
        var result = await _service.GetIban(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.Iban.ShouldBeNullOrEmpty();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.ShouldHaveSingleItem();
        result.ProblemDetail.Errors[0].Code.ShouldBe("UserNotFound");
        result.ProblemDetail.Errors[0].Message.ShouldBe("User not found");
    }

    [Fact]
    public async Task GetIban_ShouldLogInformationMessages_WhenSuccessful()
    {
        // Arrange
        const int userId = 123;
        var request = new GetIbanRequest { UserId = userId };
        const string expectedIban = "**************************";

        var queryResponse = new GetUserIbanResponse(expectedIban);
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetIbanByUserIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        await _service.GetIban(request, _mockContext.Object);

        // Assert
        VerifyLogWasCalled(LogLevel.Information, "Initiating GetIban request.");
        VerifyLogWasCalled(LogLevel.Information, "Successfully retrieved IBAN.");
    }

    [Fact]
    public async Task GetIban_ShouldLogWarning_WhenInvalidUserId()
    {
        // Arrange
        const int invalidUserId = 0;
        var request = new GetIbanRequest { UserId = invalidUserId };

        // Act
        await _service.GetIban(request, _mockContext.Object);

        // Assert
        VerifyLogWasCalled(LogLevel.Warning, "Invalid UserId provided:");
    }

    [Fact]
    public async Task GetIban_ShouldLogWarning_WhenMediatorReturnsError()
    {
        // Arrange
        const int userId = 123;
        var request = new GetIbanRequest { UserId = userId };

        var error = Error.Validation("TestError", "Test error description");
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetIbanByUserIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(error);

        // Act
        await _service.GetIban(request, _mockContext.Object);

        // Assert
        VerifyLogWasCalled(LogLevel.Warning, "GetIban failed. Errors:");
    }

    [Fact]
    public async Task GetIban_ShouldCallMediator_WhenValidRequest()
    {
        // Arrange
        const int userId = 123;
        var request = new GetIbanRequest { UserId = userId };

        var queryResponse = new GetUserIbanResponse("**************************");
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetIbanByUserIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(queryResponse);

        // Act
        await _service.GetIban(request, _mockContext.Object);

        // Assert
        _mockMediator.Verify(x => x.Send(
            It.Is<GetIbanByUserIdQuery>(q => q.UserId == userId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetIban_ShouldMapErrorCorrectly_WhenSingleErrorReturned()
    {
        // Arrange
        const int userId = 123;
        var request = new GetIbanRequest { UserId = userId };

        var error = Error.NotFound("IbanNotFound", "IBAN not found for user");
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetIbanByUserIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(error);

        // Act
        var result = await _service.GetIban(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.Iban.ShouldBeNullOrEmpty();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.ShouldHaveSingleItem();
        result.ProblemDetail.Errors[0].Code.ShouldBe("IbanNotFound");
        result.ProblemDetail.Errors[0].Message.ShouldBe("IBAN not found for user");
    }

    [Fact]
    public async Task GetIban_ShouldMapMultipleErrors_WhenMultipleErrorsReturned()
    {
        // Arrange
        const int userId = 123;
        var request = new GetIbanRequest { UserId = userId };

        var errors = new List<Error>
        {
            Error.Validation("ValidationError1", "First validation error"),
            Error.Validation("ValidationError2", "Second validation error")
        };
        _mockMediator.Setup(x => x.Send(
                It.IsAny<GetIbanByUserIdQuery>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(errors);

        // Act
        var result = await _service.GetIban(request, _mockContext.Object);

        // Assert
        result.ShouldNotBeNull();
        result.Iban.ShouldBeNullOrEmpty();
        result.ProblemDetail.ShouldNotBeNull();
        result.ProblemDetail.Errors.Count.ShouldBe(2);
        result.ProblemDetail.Errors[0].Code.ShouldBe("ValidationError1");
        result.ProblemDetail.Errors[0].Message.ShouldBe("First validation error");
        result.ProblemDetail.Errors[1].Code.ShouldBe("ValidationError2");
        result.ProblemDetail.Errors[1].Message.ShouldBe("Second validation error");
    }

    #endregion

    private void VerifyLogWasCalled(LogLevel level, string message)
    {
        _mockLogger.Verify(
            x => x.Log(
                level,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}
