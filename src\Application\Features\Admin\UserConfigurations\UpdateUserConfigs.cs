﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Admin.UserConfigurations;

public sealed record UpdateUserConfigsRequest(
    [Required, StringLength(100, MinimumLength = 1)] string AcceptorCode,
    string? Iban,
    SettlementPlanType PlanType,
    byte IbanUpdateMaxChangesPerWindow,
    byte IbanUpdateTimeWindowHours,
    bool IsCritical,
    bool IsFree,
    bool IsDepositActivate,
    bool IsBanned,
    WageType WageType,
    decimal WageValue,
    int Max,
    int Min,
    int MaxSettlementAmount,
    decimal DailyTransferLimit);

public sealed class UpdateUserConfigsController : ApiControllerBase
{
    /// <summary>
    /// Updates user configurations. Requires admin privileges.
    /// </summary>
    /// <param name="userId">Target user ID</param>
    /// <param name="request">Configuration values</param>
    [HttpPut("admin/{userId:int}/user-configs")]
    [Authorize("serviceAdministration")]
    [ProducesResponseType<Success>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UpdateUserConfigs(
        [FromRoute] int userId,
        [FromBody] UpdateUserConfigsRequest request)
    {
        var command = new UpdateUserConfigsCommand(
            userId,
            request.AcceptorCode,
            request.Iban,
            request.PlanType,
            request.IbanUpdateMaxChangesPerWindow,
            request.IbanUpdateTimeWindowHours,
            request.IsCritical,
            request.IsFree,
            request.IsDepositActivate,
            request.IsBanned,
            request.WageType,
            request.WageValue,
            request.Max,
            request.Min,
            request.MaxSettlementAmount,
            request.DailyTransferLimit);

        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(_ => Ok(Result.Success), Problem);
    }
}

public sealed record UpdateUserConfigsCommand(
    int UserId,
    string AcceptorCode,
    string? Iban,
    SettlementPlanType PlanType,
    byte IbanUpdateMaxChangesPerWindow,
    byte IbanUpdateTimeWindowHours,
    bool IsCritical,
    bool IsFree,
    bool IsDepositActivate,
    bool IsBanned,
    WageType WageType,
    decimal WageValue,
    int Max,
    int Min,
    int MaxSettlementAmount,
    decimal DailyTransferLimit)
    : IRequest<UpdateUserConfigsCommand, Task<ErrorOr<Success>>>;

public sealed class UpdateUserConfigsCommandHandler(IApplicationDbContext dbContext)
    : IRequestHandler<UpdateUserConfigsCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(
        UpdateUserConfigsCommand request,
        CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .AsTracking()
            .Where(x => x.UserId == request.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null)
            return Error.NotFound(description: $"User configuration for user ID {request.UserId} not found.");

        userConfig.AcceptorCode = request.AcceptorCode.Trim();
        userConfig.PlanType = request.PlanType;
        userConfig.IsCritical = request.IsCritical;
        userConfig.IsFree = request.IsFree;
        userConfig.IsDepositActivate = request.IsDepositActivate;
        userConfig.IsBanned = request.IsBanned;
        userConfig.WageType = request.WageType;
        userConfig.WageValue = request.WageValue;
        userConfig.Max = request.Max;
        userConfig.Min = request.Min;
        userConfig.MaxSettlementAmount = request.MaxSettlementAmount;
        userConfig.DailyTransferLimit = request.DailyTransferLimit;
        userConfig.IbanUpdateMaxChangesPerWindow = request.IbanUpdateMaxChangesPerWindow;
        userConfig.IbanUpdateTimeWindowHours = request.IbanUpdateTimeWindowHours;

        // IBAN can be optional; when provided, validate and normalize
        if (request.Iban is not null)
        {
            userConfig.Iban = string.IsNullOrWhiteSpace(request.Iban)
                ? Iban.Empty()
                : Iban.Of(request.Iban);
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        if (result == 0)
            return Error.Failure(description: "No changes were saved.");

        return Result.Success;
    }
}

public sealed class UpdateUserConfigsCommandValidator : AbstractValidator<UpdateUserConfigsCommand>
{
    public UpdateUserConfigsCommandValidator()
    {
        RuleFor(x => x.UserId)
            .GreaterThan(0).WithMessage("User ID must be greater than 0");

        RuleFor(x => x.AcceptorCode)
            .NotEmpty().WithMessage("Acceptor code is required")
            .MaximumLength(100).WithMessage("Acceptor code must be at most 100 characters");

        When(x => !string.IsNullOrWhiteSpace(x.Iban), () =>
        {
            RuleFor(x => x.Iban!)
                .Length(26).WithMessage("IBAN must be exactly 26 characters")
                .Must(iban => iban.StartsWith("IR"))
                    .WithMessage("IBAN must start with 'IR'")
                .Must(iban => iban[2..].All(char.IsDigit))
                    .WithMessage("IBAN must contain only digits after 'IR'");
        });

        RuleFor(x => x.WageValue)
            .GreaterThanOrEqualTo(0).WithMessage("Wage value must be non-negative");

        When(x => x.WageType == WageType.Percent, () =>
        {
            RuleFor(x => x.WageValue)
                .InclusiveBetween(0, 100)
                .WithMessage("Percent wage value must be between 0 and 100");
        });

        RuleFor(x => x.Min)
            .GreaterThanOrEqualTo(0);

        RuleFor(x => x.Max)
            .GreaterThanOrEqualTo(x => x.Min)
            .WithMessage("Max must be greater than or equal to Min");

        RuleFor(x => x.MaxSettlementAmount)
            .GreaterThanOrEqualTo(0);

        RuleFor(x => x.DailyTransferLimit)
            .GreaterThanOrEqualTo(0);

        RuleFor(x => x.IbanUpdateMaxChangesPerWindow)
            .InclusiveBetween((byte)0, (byte)100);

        RuleFor(x => x.IbanUpdateTimeWindowHours)
            .InclusiveBetween((byte)1, (byte)168);
    }
}
