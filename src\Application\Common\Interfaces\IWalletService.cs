﻿using ErrorOr;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Services.Wallet.Models;

namespace Zify.Settlement.Application.Common.Interfaces;

public interface IWalletService
{
    Task<ErrorOr<WalletAccessBalanceResponse>> GetWalletAccessBalanceById(
        WalletId walletId,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<CreateSettlementWalletResponse>> CreateSettlementWallet(
        int userId,
        Guid paymentWalletId,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<FreezeOrderAmountResponse>> BlockOrderAmount(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<WalletSnapshotResponse>> GetWalletSnapshot(
        Guid walletId,
        DateTime targetTime,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<Success>> ChargeSettlementWallet(
        Guid paymentWalletId,
        Guid settlementWalletId,
        Guid correlationId,
        decimal amount,
        string? description = null,
        CancellationToken cancellationToken = default);
}
