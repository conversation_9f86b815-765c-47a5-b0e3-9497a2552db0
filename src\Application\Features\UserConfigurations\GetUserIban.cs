﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public sealed record GetUserIbanResponse(string? Iban);

public class GetUserIbanController : ApiControllerBase
{
    /// <summary>
    /// Retrieves the IBAN associated with the current user.
    /// </summary>
    /// <returns>
    /// An <see cref="GetUserIbanResponse"/> containing the user's IBAN if successful, or a problem response
    /// if an error occurs.
    /// </returns>
    /// <response code="200">Returns the user's IBAN in the response body.</response>
    /// <response code="400">Returns a problem response if the request is invalid or the user is not found.</response>
    [HttpGet("users/get-iban")]
    [Authorize("read")]
    [ProducesResponseType<GetUserIbanResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetIban([FromServices] ICurrentUserService currentUserService)
    {
        var result = await Mediator.Send(
            new GetIbanByUserIdQuery(currentUserService.UserId),
            HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record GetIbanByUserIdQuery(int? UserId)
    : IRequest<GetIbanByUserIdQuery, Task<ErrorOr<GetUserIbanResponse>>>;

public sealed class GetIbanByUserIdQueryHandler(
    IApplicationDbContext dbContext,
    IRedisCacheService cacheService)
    : IRequestHandler<GetIbanByUserIdQuery, Task<ErrorOr<GetUserIbanResponse>>>
{
    public async Task<ErrorOr<GetUserIbanResponse>> Handle(GetIbanByUserIdQuery request, CancellationToken cancellationToken)
    {
        var userConfig = await cacheService.GetOrSetAsync(
            GetCacheKey(request.UserId),
            async () =>
            {
                return await dbContext.UserConfigs
                    .AsNoTracking()
                    .Where(x => x.UserId == request.UserId)
                    .Select(x => new { x.Iban, x.UserId })
                    .FirstOrDefaultAsync(cancellationToken);
            },
            absoluteExpiration: TimeSpan.FromMinutes(30),
            cancellationToken: cancellationToken);

        if (userConfig == null)
        {
            return Error.NotFound(description: "کاربر یافت نشد");
        }

        return new GetUserIbanResponse(userConfig.Iban);
    }
    private static string GetCacheKey(int? userId) => $"UserIban_{userId}";
}

public sealed class GetIbanByUserIdQueryValidator : AbstractValidator<GetIbanByUserIdQuery>
{
    public GetIbanByUserIdQueryValidator()
    {
        RuleFor(x => x.UserId)
            .NotNull().WithMessage("User ID is required.")
            .GreaterThan(0).WithMessage("User ID must be greater than 0.");
    }
}
